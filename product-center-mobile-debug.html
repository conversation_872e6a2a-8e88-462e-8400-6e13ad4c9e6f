<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>产品中心 - 调试版</title>
  <script src="https://cdn.bootcdn.net/ajax/libs/vue/3.3.4/vue.global.min.js"></script>
  <style>
    body {
      margin: 0;
      background: #f5f6fa;
      color: #222;
      font-size: 14px;
      font-family: Arial, sans-serif;
    }
    
    .container {
      max-width: 100%;
      margin: 0 auto;
      padding: 16px;
    }
    
    .loading-container {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 40px 0;
      color: #666;
    }
    
    .loading-spinner {
      width: 20px;
      height: 20px;
      border: 2px solid #e1e4e8;
      border-top: 2px solid #2256a0;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: 8px;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .product-list {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }
    
    .product-article {
      background: #fff;
      border-radius: 12px;
      box-shadow: 0 2px 12px rgba(34,86,160,0.06);
      padding: 20px;
      margin-bottom: 20px;
    }
    
    .product-title {
      font-size: 1.2rem;
      font-weight: 700;
      color: #2256a0;
      margin-bottom: 12px;
    }
    
    .debug-info {
      background: #f0f0f0;
      padding: 10px;
      margin: 10px 0;
      border-radius: 4px;
      font-family: monospace;
      font-size: 12px;
    }
  </style>
</head>
<body>
  <div id="app">
    <div class="container">
      <h1>产品中心调试版</h1>
      
      <!-- 调试信息 -->
      <div class="debug-info">
        <div>Loading: {{ loading }}</div>
        <div>Products Length: {{ products.length }}</div>
        <div>Error: {{ error }}</div>
        <div>API URL: /wp-json/wp/v2/posts?_embed&per_page=100&tags=7</div>
      </div>
      
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <span>正在加载产品信息...</span>
      </div>
      
      <!-- 产品列表 -->
      <div v-else-if="products.length > 0" class="product-list">
        <div v-for="(item, index) in products" :key="item.id" class="product-article">
          <div class="product-title">{{ item.title }}</div>
          <div class="debug-info">
            <div>Product {{ index + 1 }} ID: {{ item.id }}</div>
            <div>Content Length: {{ item.content ? item.content.length : 0 }}</div>
          </div>
          <div v-html="item.content"></div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-else class="loading-container">
        <span>暂无产品信息</span>
      </div>
      
      <!-- 错误状态 -->
      <div v-if="error" class="debug-info" style="background: #ffebee; color: #c62828;">
        错误信息: {{ error }}
      </div>
    </div>
  </div>

  <script>
    const { createApp, ref, onMounted } = Vue;
    
    createApp({
      setup() {
        const tagId = 7;
        const products = ref([]);
        const loading = ref(true);
        const error = ref(null);

        async function fetchProducts() {
          loading.value = true;
          error.value = null;
          
          try {
            console.log('开始获取产品数据...');
            const response = await fetch(`/wp-json/wp/v2/posts?_embed&per_page=100&tags=${tagId}`);
            
            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);
            
            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            console.log('获取到的原始数据:', data);
            console.log('数据数量:', data.length);
            
            products.value = data.map((post, index) => {
              console.log(`处理产品 ${index + 1}:`, post.title?.rendered);
              
              let img = '';
              if (post._embedded && post._embedded['wp:featuredmedia'] && post._embedded['wp:featuredmedia'][0]) {
                img = post._embedded['wp:featuredmedia'][0].source_url;
              }
              
              return {
                id: post.id,
                img: img || '',
                title: post.title && post.title.rendered ? post.title.rendered : '无标题',
                content: post.content && post.content.rendered ? post.content.rendered : ''
              };
            });
            
            console.log('处理后的产品数据:', products.value);
            
          } catch (e) {
            console.error('获取产品数据时出错:', e);
            error.value = e.message;
            products.value = [];
          } finally {
            loading.value = false;
          }
        }

        onMounted(() => {
          console.log('组件已挂载，开始获取数据');
          fetchProducts();
        });

        return {
          products,
          loading,
          error
        };
      }
    }).mount('#app');
  </script>
</body>
</html>
