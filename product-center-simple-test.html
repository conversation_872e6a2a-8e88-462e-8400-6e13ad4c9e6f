<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>产品中心 - 简单测试</title>
  <script src="https://cdn.bootcdn.net/ajax/libs/vue/3.3.4/vue.global.min.js"></script>
  <style>
    body {
      margin: 0;
      background: #f5f6fa;
      color: #222;
      font-size: 14px;
      font-family: Arial, sans-serif;
      padding: 20px;
    }
    
    .debug-info {
      background: #f0f0f0;
      padding: 10px;
      margin: 10px 0;
      border-radius: 4px;
      font-size: 12px;
      border: 1px solid #ccc;
    }
    
    .product-item {
      background: #fff;
      border: 2px solid #2256a0;
      border-radius: 8px;
      padding: 15px;
      margin: 15px 0;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    
    .product-title {
      font-size: 18px;
      font-weight: bold;
      color: #2256a0;
      margin-bottom: 10px;
    }
    
    .loading {
      text-align: center;
      padding: 20px;
      font-size: 16px;
    }
  </style>
</head>
<body>
  <div id="app">
    <h1>产品中心 - 简单测试版</h1>
    
    <!-- 详细调试信息 -->
    <div class="debug-info">
      <h3>调试信息</h3>
      <div><strong>Loading:</strong> {{ loading }}</div>
      <div><strong>Products Array:</strong> {{ products }}</div>
      <div><strong>Products Length:</strong> {{ products.length }}</div>
      <div><strong>Error:</strong> {{ error }}</div>
      <div><strong>API URL:</strong> /wp-json/wp/v2/posts?_embed&per_page=100&tags=7</div>
    </div>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="loading">
      正在加载产品信息...
    </div>
    
    <!-- 产品列表 - 最简单的显示方式 -->
    <div v-if="!loading">
      <h2>产品列表 (共 {{ products.length }} 个产品)</h2>
      
      <div v-for="(product, index) in products" :key="product.id" class="product-item">
        <div class="product-title">
          产品 {{ index + 1 }}: {{ product.title }}
        </div>
        <div class="debug-info">
          <div>ID: {{ product.id }}</div>
          <div>Content Length: {{ product.content ? product.content.length : 0 }}</div>
          <div>Has Image: {{ product.img ? 'Yes' : 'No' }}</div>
        </div>
        <div v-html="product.content"></div>
      </div>
      
      <!-- 如果没有产品 -->
      <div v-if="products.length === 0" class="loading">
        没有找到产品数据
      </div>
    </div>
    
    <!-- 错误信息 -->
    <div v-if="error" class="debug-info" style="background: #ffebee; border-color: #f44336;">
      <h3>错误信息</h3>
      <div>{{ error }}</div>
    </div>
  </div>

  <script>
    const { createApp, ref, onMounted } = Vue;
    
    createApp({
      setup() {
        const tagId = 7;
        const products = ref([]);
        const loading = ref(true);
        const error = ref(null);

        async function fetchProducts() {
          loading.value = true;
          error.value = null;
          
          try {
            console.log('=== 开始获取产品数据 ===');
            const url = `/wp-json/wp/v2/posts?_embed&per_page=100&tags=${tagId}`;
            console.log('请求URL:', url);
            
            const response = await fetch(url);
            console.log('响应状态:', response.status);
            console.log('响应头:', Object.fromEntries(response.headers.entries()));
            
            if (!response.ok) {
              throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
            const data = await response.json();
            console.log('原始数据:', data);
            console.log('数据类型:', typeof data);
            console.log('是否为数组:', Array.isArray(data));
            console.log('数据长度:', data.length);
            
            if (!Array.isArray(data)) {
              throw new Error('API返回的数据不是数组格式');
            }
            
            const processedProducts = data.map((post, index) => {
              console.log(`--- 处理产品 ${index + 1} ---`);
              console.log('原始post对象:', post);
              console.log('标题:', post.title?.rendered);
              console.log('内容长度:', post.content?.rendered?.length || 0);
              
              let img = '';
              if (post._embedded && post._embedded['wp:featuredmedia'] && post._embedded['wp:featuredmedia'][0]) {
                img = post._embedded['wp:featuredmedia'][0].source_url;
                console.log('特色图片:', img);
              }
              
              const processedProduct = {
                id: post.id,
                img: img || '',
                title: post.title && post.title.rendered ? post.title.rendered : '无标题',
                content: post.content && post.content.rendered ? post.content.rendered : '无内容'
              };
              
              console.log('处理后的产品:', processedProduct);
              return processedProduct;
            });
            
            console.log('=== 所有产品处理完成 ===');
            console.log('处理后的产品数组:', processedProducts);
            
            products.value = processedProducts;
            
            console.log('Vue响应式数据已更新');
            console.log('products.value:', products.value);
            console.log('products.value.length:', products.value.length);
            
          } catch (e) {
            console.error('=== 获取产品数据时出错 ===');
            console.error('错误对象:', e);
            console.error('错误消息:', e.message);
            console.error('错误堆栈:', e.stack);
            
            error.value = e.message;
            products.value = [];
          } finally {
            loading.value = false;
            console.log('=== 数据获取流程结束 ===');
            console.log('最终状态 - loading:', loading.value);
            console.log('最终状态 - products.length:', products.value.length);
            console.log('最终状态 - error:', error.value);
          }
        }

        onMounted(() => {
          console.log('Vue组件已挂载，开始获取数据');
          fetchProducts();
        });

        return {
          products,
          loading,
          error
        };
      }
    }).mount('#app');
  </script>
</body>
</html>
