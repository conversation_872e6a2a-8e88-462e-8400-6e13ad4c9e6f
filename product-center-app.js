// 产品中心Vue应用
const { createApp, ref, onMounted, computed } = Vue;

const ProductCenterApp = {
  setup() {
    const tagId = 7;
    const products = ref([]);
    const loading = ref(true);
    const error = ref(null);
    
    // 计算属性
    const hasProducts = computed(() => products.value.length > 0);
    const showProducts = computed(() => !loading.value && products.value.length > 0);
    const showEmpty = computed(() => !loading.value && products.value.length === 0);
    const firstProductTitle = computed(() => products.value.length > 0 ? products.value[0].title : '');
    const firstProductContentLength = computed(() => {
      return products.value.length > 0 && products.value[0].content ? products.value[0].content.length : 0;
    });

    async function fetchProducts() {
      loading.value = true;
      error.value = null;
      
      try {
        console.log('=== 开始获取产品数据 ===');
        const url = `/wp-json/wp/v2/posts?_embed&per_page=100&tags=${tagId}`;
        console.log('请求URL:', url);
        
        const response = await fetch(url);
        console.log('响应状态:', response.status);
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        console.log('获取到的数据:', data);
        console.log('数据数量:', data.length);
        
        if (!Array.isArray(data)) {
          throw new Error('API返回的数据不是数组格式');
        }
        
        const processedProducts = data.map((post, index) => {
          console.log(`处理产品 ${index + 1}:`, post.title?.rendered);
          
          let img = '';
          if (post._embedded && post._embedded['wp:featuredmedia'] && post._embedded['wp:featuredmedia'][0]) {
            img = post._embedded['wp:featuredmedia'][0].source_url;
          }
          
          return {
            id: post.id,
            img: img || '',
            title: post.title && post.title.rendered ? post.title.rendered : '无标题',
            content: post.content && post.content.rendered ? post.content.rendered : '无内容'
          };
        });
        
        products.value = processedProducts;
        console.log('处理后的产品数据:', products.value);
        console.log('最终产品数量:', products.value.length);
        
      } catch (e) {
        console.error('获取产品数据时出错:', e);
        error.value = e.message;
        products.value = [];
      } finally {
        loading.value = false;
        console.log('=== 数据获取完成 ===');
        console.log('loading:', loading.value);
        console.log('products.length:', products.value.length);
        console.log('error:', error.value);
      }
    }

    // 图片懒加载优化
    function optimizeImages() {
      setTimeout(() => {
        const images = document.querySelectorAll('.product-article-content img');
        images.forEach(img => {
          img.loading = 'lazy';
          img.onerror = function() {
            this.style.display = 'none';
          };
        });
      }, 100);
    }

    onMounted(() => {
      console.log('Vue组件已挂载');
      fetchProducts().then(() => {
        optimizeImages();
      });
    });

    return {
      products,
      loading,
      error,
      hasProducts,
      showProducts,
      showEmpty,
      firstProductTitle,
      firstProductContentLength
    };
  }
};

// 等待DOM加载完成后初始化应用
document.addEventListener('DOMContentLoaded', function() {
  console.log('DOM加载完成，初始化Vue应用');
  const app = createApp(ProductCenterApp);
  
  // 如果有ElementPlus，则使用
  if (typeof ElementPlus !== 'undefined') {
    app.use(ElementPlus);
  }
  
  app.mount('#product-center-app');
  console.log('Vue应用已挂载到 #product-center-app');
});
