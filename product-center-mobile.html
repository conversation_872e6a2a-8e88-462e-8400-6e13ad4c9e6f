<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>产品中心</title>
  <!-- <link rel="stylesheet" href="https://unpkg.com/element-plus@2.9.11/dist/index.css" /> -->
  <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/element-plus/2.3.3/index.min.css">
  <link rel="stylesheet" href="/wp-content/themes/astra/style.css" />
  <link rel="stylesheet" href="/wp-content/plugins/ultimate-addons-for-gutenberg/dist/blocks.style.css">
  <!-- <link rel="stylesheet" href="/wp-content/uploads/uag-plugin/assets/1000/uag-css-915.css"> -->

  <script src="https://cdn.bootcdn.net/ajax/libs/vue/3.3.4/vue.global.min.js"></script>
  <script src="https://cdn.bootcdn.net/ajax/libs/element-plus/2.3.3/index.full.min.js"></script>
  <!-- <script src="https://unpkg.com/element-plus@2.9.11"></script> -->

  <style>
    body {
      margin: 0;
      background: #f5f6fa;
      color: #222;
      font-size: 14px; /* 移动端基础字体大小 */
    }
    .product-center-header {
      padding: 20px 0 12px 0; /* 减小头部padding */
      text-align: center;
      position: relative;
    }
    .product-list-section {
      max-width: 100%; /* 移动端宽度占满 */
      margin: 0 auto;
      padding: 16px 12px 24px 12px; /* 调整内边距 */
      display: flex;
      flex-direction: column;
      gap: 20px; /* 减小间距 */
    }
    .product-list {
      display: flex;
      flex-direction: column;
      gap: 24px; /* 减小产品卡片间距 */
      justify-content: flex-start;
      font-family: 'Noto Serif SC';
    }
    .product-article {
      background: #fff;
      border-radius: 8px; /* 减小圆角 */
      box-shadow: 0 2px 8px rgba(34,86,160,0.08); /* 减小阴影 */
      padding: 20px 16px 24px 16px; /* 减小内边距 */
      width: 100%;
      margin: 0 auto;
      overflow: hidden;
    }
    .product-article-title {
      font-size: 1.25rem; /* 标题字号调小 */
      font-weight: 700;
      color: #2256a0;
      margin-bottom: 12px;
      line-height: 1.2;
    }
    .product-article-img {
      width: 100%;
      max-width: 100%; /* 图片宽度占满容器 */
      display: block;
      margin: 0 auto 16px auto;
      border-radius: 6px;
      object-fit: cover;
    }
    .product-article-content {
      color: #222;
      font-size: 1rem; /* 内容字号调小 */
      line-height: 1.6;
      word-break: break-all;
      background: #fff;
      padding: 0;
    }
    /* 适配WordPress内容常用样式 */
    .product-article-content h2, .product-article-content h3 {
      color: #2256a0;
      margin: 1em 0 0.5em 0;
      font-size: 1.2em; /* 调小标题字号 */
    }
    .product-article-content img {
      max-width: 100%;
      border-radius: 4px;
      margin: 12px 0;
      display: block;
    }
    .product-article-content p {
      margin: 0.6em 0;
    }
    .product-article-content ul, .product-article-content ol {
      margin: 0.8em 0 0.8em 1.5em; /* 减小列表缩进 */
    }

    /* 添加移动端优化样式 */
    @media screen and (max-width: 480px) {
      .product-article-content {
        font-size: 0.95rem;
      }
      .product-list-section {
        padding: 12px 8px 20px 8px;
      }
      .product-article {
        padding: 16px 12px 20px 12px;
      }
    }

    @media screen and (max-width: 360px) {
      .product-list-section {
        padding: 10px 6px 16px 6px;
      }
      .product-article {
        padding: 14px 10px 18px 10px;
      }
      .product-article-title {
        font-size: 1.1rem;
      }
      .product-article-content {
        font-size: 0.9rem;
      }
    }

    /* 覆盖动态加载的UAG插件样式，适配移动端 */

    /* 容器布局优化 */
    .wp-block-uagb-container {
      margin-left: 0 !important;
      margin-right: 0 !important;
      padding-left: 8px !important;
      padding-right: 8px !important;
    }

    .wp-block-uagb-container.uagb-is-root-container {
      max-width: 100% !important;
      width: 100% !important;
    }

    .wp-block-uagb-container .uagb-container-inner-blocks-wrap {
      flex-direction: column !important;
      align-items: stretch !important;
      flex-wrap: wrap !important;
    }

    /* 标题优化 */
    /* .wp-block-uagb-advanced-heading .uagb-heading-text {
      font-size: 1.2rem !important;
      line-height: 1.3 !important;
      text-align: center !important;
      margin-bottom: 10px !important;
    } */

    /* 图片优化 */
    .wp-block-uagb-image .wp-block-uagb-image__figure img {
      width: 100% !important;
      height: auto !important;
      max-width: 100% !important;
    }

    /* 图标列表优化 */
    .wp-block-uagb-icon-list .uagb-icon-list__wrap {
      flex-direction: column !important;
      align-items: flex-start !important;
    }

    .wp-block-uagb-icon-list .wp-block-uagb-icon-list-child {
      margin-bottom: 8px !important;
      width: 100% !important;
    }

    .wp-block-uagb-icon-list .uagb-icon-list__label {
      font-size: 0.9rem !important;
      line-height: 1.4 !important;
    }

    /* 移动端专用覆盖样式 */
    @media screen and (max-width: 767px) {
      /* 重置所有固定宽度和边距 */
      .wp-block-uagb-container.uagb-block-2d55f205,
      .wp-block-uagb-container.uagb-block-053cbf48,
      .wp-block-uagb-container.uagb-block-b588028b,
      .wp-block-uagb-container.uagb-block-43a1f41c,
      .wp-block-uagb-container.uagb-block-26f6ce3e,
      .wp-block-uagb-container.uagb-block-9549678f,
      .wp-block-uagb-container.uagb-block-90c47777,
      .wp-block-uagb-container.uagb-block-d6ecddc8,
      .wp-block-uagb-container.uagb-block-de8a37e7 {
        max-width: 100% !important;
        width: 100% !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
        padding-left: 8px !important;
        padding-right: 8px !important;
        margin-top: 10px !important;
        margin-bottom: 10px !important;
      }

      /* 特殊容器的边距调整 */
      .wp-block-uagb-container.uagb-block-b588028b {
        padding-left: 8px !important; /* 覆盖原来的50px */
      }

      .wp-block-uagb-container.uagb-block-9549678f {
        margin-right: 0 !important; /* 覆盖原来的220px */
      }

      .wp-block-uagb-container.uagb-block-90c47777 {
        margin-left: 0 !important; /* 覆盖原来的240px */
      }

      /* 标题字体大小调整 */
      .wp-block-uagb-advanced-heading.uagb-block-37dbb4dc .uagb-heading-text {
        font-size: 1.5rem !important; /* 覆盖原来的36px */
      }

      /* 容器内部布局优化 */
      .wp-block-uagb-container .uagb-container-inner-blocks-wrap {
        flex-direction: column !important;
        align-items: stretch !important;
        justify-content: flex-start !important;
        flex-wrap: wrap !important;
        row-gap: 10px !important;
        column-gap: 0 !important;
      }

      /* 图片容器优化 */
      .wp-block-uagb-image .wp-block-uagb-image__figure {
        margin: 0 auto 15px auto !important;
        max-width: 100% !important;
      }

      /* 图标列表移动端优化 */
      .wp-block-uagb-icon-list .uagb-icon-list__wrap {
        align-items: flex-start !important;
        justify-content: flex-start !important;
      }

      .wp-block-uagb-icon-list .uagb-icon-list__label {
        text-align: left !important;
      }
    }

    /* 超小屏幕优化 */
    @media screen and (max-width: 480px) {
      .wp-block-uagb-container {
        padding-left: 4px !important;
        padding-right: 4px !important;
      }

      .wp-block-uagb-advanced-heading .uagb-heading-text {
        font-size: 1.1rem !important;
      }

      .wp-block-uagb-advanced-heading.uagb-block-37dbb4dc .uagb-heading-text {
        font-size: 1.3rem !important;
      }

      .wp-block-uagb-icon-list .uagb-icon-list__label {
        font-size: 0.85rem !important;
      }
    }
  </style>
</head>
<body>
  <div id="app">
    <!-- <div class="product-center-header">
      <h1>产品中心</h1>
    </div> -->
    <div class="product-list-section">
      <!-- <el-skeleton v-if="loading" rows="6" animated style="width:100%" /> -->
      <div>
        <div class="product-list">
          <div class="product-article" v-for="item in products" :key="item.id">
            <link
              :rel="'stylesheet'"
              :href="`/wp-content/uploads/uag-plugin/assets/1000/uag-css-${item.id}.css`"
            />
            <div class="product-article-content" v-html="item.content"></div>
          </div>
        </div>
        <!-- <el-pagination
          v-if="total > pageSize"
          background
          layout="prev, pager, next"
          :total="total"
          :page-size="pageSize"
          :current-page.sync="currentPage"
          @current-change="handlePageChange"
          style="margin-top:32px;"
        /> -->
      </div>
    </div>
  </div>
  <script>
    const { createApp, ref, onMounted } = Vue;
    createApp({
      setup() {
        const tagId = 7;
        const products = ref([]);
        const loading = ref(true);
        const total = ref(0);
        const pageSize = 100; // 获取所有产品，不分页
        const currentPage = ref(1);

        async function fetchProducts() {
          loading.value = true;
          try {
            const response = await fetch(`/wp-json/wp/v2/posts?_embed&per_page=${pageSize}&page=${currentPage.value}&tags=${tagId}`);
            const data = await response.json();
            console.log(data);
            // 获取总数
            const totalCount = response.headers.get('X-WP-Total');
            total.value = totalCount ? parseInt(totalCount) : 0;
            products.value = data.map(post => {
              let img = '';
              if (post._embedded && post._embedded['wp:featuredmedia'] && post._embedded['wp:featuredmedia'][0]) {
                img = post._embedded['wp:featuredmedia'][0].source_url;
              }
              return {
                id: post.id,
                img: img || '',
                title: post.title && post.title.rendered ? post.title.rendered : '无标题',
                content: post.content && post.content.rendered ? post.content.rendered : ''
              };
            });
          } catch (e) {
            products.value = [];
            total.value = 0;
          } finally {
            loading.value = false;
          }
        }

        function handlePageChange(page) {
          currentPage.value = page;
          fetchProducts();
        }

        onMounted(fetchProducts);
        // watch(currentPage, () => fetchProducts());

        return {
          products,
          loading,
          total,
          pageSize,
          currentPage,
          handlePageChange
        };
      }
    }).use(ElementPlus).mount('#app');
  </script>
</body>
</html>