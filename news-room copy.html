<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>NEWS ROOM 新闻中心</title>
  <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/element-plus/2.3.3/index.min.css">
  <script src="https://cdn.bootcdn.net/ajax/libs/vue/3.3.4/vue.global.min.js"></script>
  <script src="https://cdn.bootcdn.net/ajax/libs/element-plus/2.3.3/index.full.min.js"></script>
  <style>
    body {
      margin: 0;
      background: #f5f6fa;
      color: #222;
    }
    .newsroom-header {
      padding: 32px 0 16px 0;
      text-align: center;
      position: relative;
    }
    .newsroom-header .subtitle {
      position: absolute;
      left: 32px;
      top: 24px;
      color: #3a5ba0;
      font-size: 14px;
      letter-spacing: 2px;
    }
    .newsroom-header .decor {
      position: absolute;
      right: 32px;
      top: 24px;
      display: flex;
      flex-direction: column;
      gap: 10px;
      z-index: 1000;
    }
    .newsroom-header .decor-dot {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      border: 2px solid #2256a0;
      background: #fff;
      transition: background 0.2s, border 0.2s;
      box-sizing: border-box;
      cursor: pointer;
    }
    .newsroom-header .decor-dot.active {
      background: #2256a0;
      border: 2px solid #2256a0;
    }
    .newsroom-header h1 {
      font-size: 2.8rem;
      color: #2256a0;
      margin: 0;
      letter-spacing: 2px;
      font-weight: 600;
    }
    .carousel-section {
      align-items: center;
      margin: 0 auto 32px auto;
      max-width: 900px;
      width: 100%;
      border-radius: 12px;
      padding: 24px 0 16px 0;
      position: relative;
      font-family: 'Noto Serif SC';
    }
    .carousel-card {
      box-shadow: 0 2px 8px rgba(34,86,160,0.10);
      border: 1.5px solid #e0e6f6;
      overflow: hidden;
      position: relative;
      width: 100%;
      min-height: 320px;
      background: #fff;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      background-color: rgb(237, 239, 240) !important;
    }
    .carousel-card-img {
      width: 100%;
      height: 280px;
      display: block;
      object-fit: cover;
    }
    .carousel-card-title {
      color: #b84a1b;
      font-size: 1.1rem;
      font-weight: 500;
      text-align: center;
      padding: 12px 0 10px 0;
      width: 100%;
      background: #fff;
      margin: 0;
      position: static;
      box-sizing: border-box;
    }
    .news-content-section {
      margin: 0 auto 40px auto;
      max-width: 1000px;
      background: #e9ecf3;
      padding: 22px 10px;
      display: flex;
      justify-content: center;
      font-family: 'Noto Serif SC';
    }
    .news-content-container {
      background: #fff;
      /* border-radius: 10px; */
      box-shadow: 0 2px 8px rgba(34,86,160,0.07);
      width: 90%;
      min-height: 420px;
      overflow: hidden;
      border: 1px solid #e0e6f6;
      padding: 30px;
      box-sizing: border-box;
    }
    .article-title {
      font-size: 1.8rem;
      color: #2256a0;
      margin-bottom: 20px;
      text-align: center;
    }
    .article-content {
      line-height: 1.8;
      font-size: 1rem;
    }
    .article-content img {
      max-width: 100%;
      height: auto;
      display: block;
      margin: 20px auto;
    }
    .article-content p {
      margin-bottom: 1.5em;
    }
    .loading-content {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 300px;
    }
    @media (max-width: 768px) {
      .newsroom-header h1 {
        font-size: 2.2rem;
      }
      .newsroom-header .subtitle {
        left: 16px;
        top: 16px;
        font-size: 12px;
      }
      .newsroom-header .decor {
        right: 16px;
        top: 16px;
      }
      .carousel-section {
        max-width: 100%;
        padding: 16px 8px;
        margin: 0 auto 24px auto;
      }
      .carousel-card {
        width: 100%;
        min-height: 240px;
      }
      .carousel-card-img {
        height: 180px;
        object-fit: cover;
      }
      .carousel-card-title {
        font-size: 0.95rem;
        padding: 8px 12px;
        line-height: 1.3;
      }
      .news-content-container {
        width: 95%;
        padding: 20px 15px;
      }
      .article-title {
        font-size: 1.4rem;
      }
      .article-content {
        font-size: 0.9rem;
        line-height: 1.6;
      }
      /* Element Plus 轮播图手机端优化 */
      .el-carousel {
        height: 260px !important;
      }
      .el-carousel__container {
        height: 260px !important;
      }
      .el-carousel__item {
        height: 260px !important;
      }
      .el-carousel__arrow {
        font-size: 24px !important;
        width: 36px !important;
        height: 36px !important;
      }
      .el-carousel__indicators {
        bottom: -30px !important;
      }
      .el-carousel__indicator {
        padding: 8px 6px !important;
      }
      .el-carousel__button {
        width: 8px !important;
        height: 8px !important;
      }
    }

    @media (max-width: 480px) {
      .newsroom-header h1 {
        font-size: 1.8rem;
      }
      .carousel-section {
        padding: 12px 4px;
      }
      .carousel-card {
        min-height: 200px;
      }
      .carousel-card-img {
        height: 140px;
      }
      .carousel-card-title {
        font-size: 0.85rem;
        padding: 6px 8px;
      }
      .news-content-container {
        width: 98%;
        padding: 15px 10px;
      }
      .article-content {
        font-size: 0.85rem;
      }
      /* 超小屏幕轮播图优化 */
      .el-carousel {
        height: 220px !important;
      }
      .el-carousel__container {
        height: 220px !important;
      }
      .el-carousel__item {
        height: 220px !important;
      }
      .el-carousel__arrow {
        font-size: 20px !important;
        width: 32px !important;
        height: 32px !important;
      }
    }
    .el-carousel__arrow {
      font-size: 40px !important;
      font-weight: bold !important;
      color: #fff !important;
      background: transparent !important;
      border: none !important;
      box-shadow: 0 2px 12px rgba(34,86,160,0.18);
      opacity: 1;
      transition: box-shadow 0.2s, color 0.2s;
    }
    .el-carousel__arrow:hover {
      box-shadow: 0 4px 18px rgba(34,86,160,0.28);
      color: #fff !important;
      background: transparent !important;
      border: none !important;
    }
    .el-card__body {
      padding: 10px !important;
      /* background-color: rgb(237, 239, 240) !important; */
    }
    /* 之前的样式保持不变，只添加新的加载样式 */
    .loading-container {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 300px;
    }
    .loading-spinner {
      animation: rotate 2s linear infinite;
      width: 50px;
      height: 50px;
    }
    .loading-spinner .path {
      stroke: #2256a0;
      stroke-linecap: round;
      animation: dash 1.5s ease-in-out infinite;
    }
    @keyframes rotate {
      100% {
        transform: rotate(360deg);
      }
    }
    @keyframes dash {
      0% {
        stroke-dasharray: 1, 150;
        stroke-dashoffset: 0;
      }
      50% {
        stroke-dasharray: 90, 150;
        stroke-dashoffset: -35;
      }
      100% {
        stroke-dasharray: 90, 150;
        stroke-dashoffset: -124;
      }
    }
  </style>
</head>
<body>
  <div id="app">
    <div class="carousel-section">
      <el-carousel :height="carouselHeight" :autoplay="false" trigger="click" arrow="always" type="card" indicator-position="outside" @change="onCarouselChange">
        <el-carousel-item v-for="(item, idx) in carouselData" :key="idx">
          <el-card class="carousel-card" shadow="hover">
            <img :src="item.img" class="carousel-card-img" :alt="item.caption">
            <div class="carousel-card-title">{{ item.caption }}</div>
          </el-card>
        </el-carousel-item>
      </el-carousel>
    </div>

    <div class="news-content-section">
      <div class="news-content-container">
        <template v-if="contentLoading">
          <div class="loading-container">
            <svg class="loading-spinner" viewBox="0 0 50 50">
              <circle class="path" cx="25" cy="25" r="20" fill="none" stroke-width="5"></circle>
            </svg>
          </div>
        </template>
        <template v-else>
          <!-- <h1 class="article-title">{{ currentArticle.title }}</h1> -->
          <div class="article-content" v-html="currentArticle.content"></div>
        </template>
      </div>
    </div>
  </div>
  <script>
    const { createApp, ref, onMounted } = Vue;
    createApp({
      setup() {
        const carouselData = ref([]);
        const loading = ref(true);
        const contentLoading = ref(true);
        const currentIndex = ref(0);
        const currentArticle = ref({
          title: '',
          content: ''
        });
        const articlesContent = ref([]);
        const carouselHeight = ref('360px');

        async function fetchNews() {
          try {
            loading.value = true;
            const response = await fetch('/wp-json/wp/v2/posts?_embed&per_page=99&page=1&tags=8');
            const posts = await response.json();
            carouselData.value = posts.map(post => {
              let img = '';
              if (post._embedded && post._embedded['wp:featuredmedia'] && post._embedded['wp:featuredmedia'][0]) {
                img = post._embedded['wp:featuredmedia'][0].source_url;
              }
              return {
                img: img || '',
                caption: post.title && post.title.rendered ? post.title.rendered : '无标题',
                id: post.id
              };
            });
            
            if (carouselData.value.length > 0) {
              await fetchArticlesContent();
            }
          } catch (e) {
            console.error('Error fetching news:', e);
            carouselData.value = [];
          } finally {
            loading.value = false;
          }
        }

        async function fetchArticlesContent() {
          try {
            contentLoading.value = true;
            const contents = await Promise.all(
              carouselData.value.map(item => 
                fetch(`/wp-json/wp/v2/posts/${item.id}`).then(res => res.json())
              )
            );
            
            articlesContent.value = contents.map(post => ({
              id: post.id,
              title: post.title.rendered,
              content: post.content.rendered
            }));
            
            if (articlesContent.value.length > 0) {
              currentArticle.value = articlesContent.value[0];
            }
          } catch (e) {
            console.error('Error fetching article content:', e);
          } finally {
            contentLoading.value = false;
          }
        }

        function onCarouselChange(idx) {
          currentIndex.value = idx;
          contentLoading.value = true;
          setTimeout(() => {
            if (articlesContent.value[idx]) {
              currentArticle.value = articlesContent.value[idx];
            }
            contentLoading.value = false;
          }, 300); // 添加短暂延迟让用户看到加载效果
        }

        function updateCarouselHeight() {
          const width = window.innerWidth;
          if (width <= 480) {
            carouselHeight.value = '220px';
          } else if (width <= 768) {
            carouselHeight.value = '260px';
          } else {
            carouselHeight.value = '360px';
          }
        }

        onMounted(() => {
          fetchNews();
          updateCarouselHeight();
          window.addEventListener('resize', updateCarouselHeight);
        });

        return {
          carouselData,
          loading,
          contentLoading,
          currentIndex,
          currentArticle,
          carouselHeight,
          onCarouselChange
        };
      }
    }).use(ElementPlus).mount('#app');
  </script>
</body>
</html>