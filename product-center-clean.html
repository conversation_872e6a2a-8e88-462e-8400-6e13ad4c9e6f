<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>产品中心</title>
  <link rel="stylesheet" href="https://cdn.bootcdn.net/ajax/libs/element-plus/2.3.3/index.min.css">
  <script src="https://cdn.bootcdn.net/ajax/libs/vue/3.3.4/vue.global.min.js"></script>
  <script src="https://cdn.bootcdn.net/ajax/libs/element-plus/2.3.3/index.full.min.js"></script>

  <style>
    body {
      margin: 0;
      background: #f5f6fa;
      color: #222;
      font-size: 14px;
    }
    
    .product-center-header {
      padding: 20px 0 12px 0;
      text-align: center;
      position: relative;
    }
    
    .product-list-section {
      max-width: 100%;
      margin: 0 auto;
      padding: 16px 12px 24px 12px;
      display: flex;
      flex-direction: column;
      gap: 20px;
    }
    
    .loading-container {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 40px 0;
      color: #666;
    }
    
    .loading-spinner {
      width: 20px;
      height: 20px;
      border: 2px solid #e1e4e8;
      border-top: 2px solid #2256a0;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-right: 8px;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .product-list {
      display: flex;
      flex-direction: column;
      gap: 20px;
      justify-content: flex-start;
      font-family: 'Noto Serif SC', serif;
    }
    
    .product-article {
      background: #fff;
      border-radius: 12px;
      box-shadow: 0 2px 12px rgba(34,86,160,0.06);
      padding: 20px 16px 24px 16px;
      width: 100%;
      margin: 0 auto;
      overflow: hidden;
      transition: box-shadow 0.3s ease, transform 0.2s ease;
      position: relative;
    }
    
    .product-article:active {
      transform: translateY(1px);
      box-shadow: 0 1px 6px rgba(34,86,160,0.1);
    }
    
    .product-article-title {
      font-size: 1.2rem;
      font-weight: 700;
      color: #2256a0;
      margin-bottom: 12px;
      line-height: 1.3;
      word-break: break-word;
    }
    
    .product-article-content {
      color: #333;
      font-size: 0.95rem;
      line-height: 1.6;
      word-break: break-word;
      background: #fff;
      padding: 0;
    }
    
    .product-article-content h2, 
    .product-article-content h3 {
      color: #2256a0;
      margin: 1.2em 0 0.6em 0;
      font-size: 1.1em;
      font-weight: 600;
      line-height: 1.4;
    }
    
    .product-article-content img {
      max-width: 100%;
      height: auto;
      border-radius: 6px;
      margin: 12px 0;
      display: block;
      background: #f8f9fa;
    }
    
    .product-article-content p {
      margin: 0.8em 0;
      text-align: justify;
    }
    
    .product-article-content ul, 
    .product-article-content ol {
      margin: 0.8em 0 0.8em 1.2em;
      padding-left: 0;
    }
    
    .product-article-content li {
      margin: 0.4em 0;
    }
    
    .debug-info {
      background: #f0f0f0;
      padding: 10px;
      margin: 10px 0;
      border-radius: 4px;
      font-size: 12px;
      border: 1px solid #ddd;
    }
    
    .product-header {
      background: #e3f2fd;
      padding: 8px;
      margin-bottom: 8px;
      border-radius: 4px;
      font-size: 11px;
      color: #1976d2;
      font-weight: bold;
    }
    
    /* 响应式设计 */
    @media screen and (max-width: 480px) {
      .product-list-section {
        padding: 12px 8px 20px 8px;
        gap: 16px;
      }
      
      .product-article {
        padding: 16px 12px 20px 12px;
        border-radius: 10px;
      }
      
      .product-article-title {
        font-size: 1.1rem;
        margin-bottom: 10px;
      }
      
      .product-article-content {
        font-size: 0.9rem;
        line-height: 1.5;
      }
    }
    
    @media screen and (max-width: 360px) {
      .product-list-section {
        padding: 10px 6px 16px 6px;
      }
      
      .product-article {
        padding: 14px 10px 18px 10px;
      }
      
      .product-article-title {
        font-size: 1.05rem;
      }
      
      .product-article-content {
        font-size: 0.85rem;
      }
    }
  </style>
</head>
<body>
  <div id="product-center-app">
    <div class="product-list-section">
      <!-- 调试信息 -->
      <div class="debug-info">
        <div><strong>Loading:</strong> {{ loading }}</div>
        <div><strong>Products Length:</strong> {{ products.length }}</div>
        <div><strong>Error:</strong> {{ error }}</div>
        <div v-show="hasProducts"><strong>First Product:</strong> {{ firstProductTitle }}</div>
        <div v-show="hasProducts"><strong>Content Length:</strong> {{ firstProductContentLength }}</div>
      </div>

      <!-- 加载状态 -->
      <div v-show="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <span>正在加载产品信息...</span>
      </div>

      <!-- 产品列表 -->
      <div v-show="showProducts" class="product-list">
        <div v-for="(item, index) in products" :key="item.id" class="product-article">
          <div class="product-header">
            产品 {{ index + 1 }}: {{ item.title }} (ID: {{ item.id }})
          </div>
          <div class="product-article-content" v-html="item.content"></div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-show="showEmpty" class="loading-container">
        <span>暂无产品信息</span>
      </div>
    </div>
  </div>

  <script src="product-center-app.js"></script>
</body>
</html>
